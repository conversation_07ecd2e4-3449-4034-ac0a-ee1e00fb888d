<?php

namespace App\Http\Controllers;

use App\Models\ClbDtl;
use App\Models\ClbType;
use App\Models\Cust;
use App\Models\Ord;
use App\Models\Profile;
use App\Models\State;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Yajra\DataTables\Facades\DataTables;
use Auth;
use App\Services\ClubPricingService;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $customerQuery = Cust::orderByDesc('CustId');

            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $customerQuery->where(function ($query) use ($searchValue) {
                    $query->where('FirstNm', 'like', "%$searchValue%")
                        ->orWhere('LastNm', 'like', "%$searchValue%")
                        ->orWhere('EMailAddr', 'like', "%$searchValue%")
                        ->orWhere('PhnNum', 'like', "%$searchValue%");
                });
            }

            if ($request->has('status') && $request->status != '') {
                $customerQuery->where('status', $request->status == 'active' ? '1' : '0');
            }

            return DataTables::of($customerQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox">';
                })
                ->addColumn('name', function ($row) {
                    $imageSrc = asset('website/' . $row->image());
                    $imageTooltip = htmlspecialchars('<img src="' . $imageSrc . '" class="img-fluid" />', ENT_QUOTES, 'UTF-8');

                    return '<img class="img-fluid preview-image" src="' . $imageSrc . '"
                 style="width: 45px;height: 45px;object-fit: cover;border-radius: 50%;"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                data-bs-html="true"
                title="' . $imageTooltip . '" /> '
                        . $row->FirstNm . ' ' . $row->LastNm;
                })
                ->addColumn('email', function ($row) {
                    return $row->EMailAddr ?? 'N/A';
                })
                ->addColumn('phone', function ($row) {
                    return $row->PhnNum ?? 'N/A';
                })
                ->addColumn('total_orders', function ($row) {
                    return count($row->custOrders);
                })
                ->addColumn('status', function ($row) {
                    return $row->StatusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item" href="' . route('customers.show', $row->CustId) . '">View Profile</a></li>
                            <li><a class="dropdown-item" href="' . route('customer.view.orders', $row->CustId) . '">View Orders</a></li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['status', 'action', 'name','checkbox'])
                ->make(true);
        }

        return view('dashboard.user-management.Customers.index');
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $customer = Cust::where('CustId', $id)->first();
        return view('dashboard.user-management.Customers.view', compact('customer'));
    }


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function orders(string $id)
    {
        return view('dashboard.user-management.Customers.order-view');

    }

    public function getRunningCustomerDetails($id){
        try {
            $detail = Cust::where('CustId', $id)->where('FtrId', Auth::user()->fitter->FtrId)->first();
            $orders = Ord::where('CustId',$detail->CustId)->get();
            return response()->json([
                'success' => true,
                'customer' => $detail,
                'orders' => $orders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch Customer details, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function clubOrderFormSubmit(Request $request)
    {
        $validationRules = [
            'customer_type' => 'required|in:new,running',
            'customer_gender' => 'required|in:male,female',
        ];
        if ($request->customer_type == 'new') {
            $validationRules = array_merge($validationRules, [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'phone_number' => 'required|string|max:15',
                'street_address' => 'required|string|max:255',
                'address_line_two' => 'nullable|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'zip_code' => 'required|string|max:10',
                'country_id' => 'required|string|max:3',
            ]);
        }

        if ($request->customer_type == 'running') {
            $validationRules = array_merge($validationRules, [
                'customer_id' => 'required|exists:cust,CustId',
                'running_email' => 'nullable|email',
                'running_phone_number' => 'nullable|string|max:15',
            ]);
        }

        $request->validate($validationRules);

        try {
            DB::beginTransaction();
            $shippingAddress = null;
            $cacheData = $request->all();
            if ($request->customer_type == "new"){
                $input = $request->all();
                $user = User::create([
                    'name' => $request->first_name . ' ' . $request->last_name,
                    'email' => $input['email'],
                    'password' => bcrypt(env('DEFAULT_PASSWORD')),
                ]);
                $profile = $user->profile;
                if ($user->profile == null) {
                    $profile = new  Profile();
                }
                if ($request->hasFile('image')) {
                    $image = $this->storeImage('fitters', $request->file('image'));
                }
                $profile->pic = $image ?? 'fitters/default.png';
                $profile->user_id = $user->id;
                $profile->address = $request->address;
                $profile->phone_number = $request->phone_number;
                $profile->save();
                $user->assignRole('customer');
                $customer = Cust::create(['user_id'=>$user->id,'DfltNameOnTag'=>$request->first_name . ' ' . $request->last_name,'FtrId' => Auth::user()->fitter->FtrId, 'AcctId' => Auth::user()->fitter->AcctId, 'LastNm' => $request->last_name, 'FirstNm' => $request->first_name, 'CityNm' => $request->city, 'Addr' => $request->street_address, 'EMailAddr' => $request->email, 'PhnNum' => $request->phone_number,'CntryCd'=>$request->country_id,'StateCd'=>$request->state,'PostalCd'=>$request->zip_code]);
                $shippingAddress = $cacheData['new_shipping_address'];
            }else if($request->customer_type == "running"){
                $customer = Cust::updateOrCreate(['CustId' => $request->customer_id, 'FtrId' => Auth::user()->fitter->FtrId], ['AcctId' => Auth::user()->fitter->AcctId, 'LastNm' => $request->running_last_name, 'FirstNm' => $request->running_first_name, 'CityNm' => $request->running_city, 'Addr' => $request->running_street_address, 'EMailAddr' => $request->running_email, 'PhnNum' => $request->running_phone_number,'DfltNameOnTag'=>$request->running_first_name . ' ' . $request->running_last_name,'StateCd'=>$request->running_state,'PostalCd'=>$request->running_zip_code]);
                $shippingAddress = $cacheData['shipping_address'];
            }
            if (isset($cacheData['clubs']) && !empty($cacheData['clubs'])) {
                // Initialize pricing service
                $pricingService = new ClubPricingService();

                // Calculate total order amount
                $totalOrderAmount = $pricingService->calculateOrderTotal($cacheData['clubs']);

                $order = new Ord();
                $order->OrdTypeCd = 'N';
                $order->CustId = $customer->CustId ?? null;
                $order->AcctId = Auth::user()->fitter->AcctId;
                $order->FtrId = Auth::user()->fitter->FtrId;
                $order->OrdNo = 'ORD-' . (Ord::max('OrdId') + 1);
                $order->OrgDt = date('Y-m-d');
                $order->NameOnTag = $customer->DfltNameOnTag;
                $order->ShipToAddr = $shippingAddress;
                $order->CustPhnNum = $customer->profile->phone_number ?? null;
                $order->CustEMailAddr = $customer->email ?? null;
                $order->OrdStatId = 10;
                $order->BillToAcctID = Auth::user()->fitter->AcctId;
                $order->TtlAmt = $totalOrderAmount; // Set total order amount
                $order->status = "pending";
                $order->save();
                foreach ($cacheData['clubs'] as $club){
                    $quantity = $club['category']['model']['quantity'] ?? 1;

                    // Calculate price for this club
                    $clubPrice = $pricingService->calculateClubPrice($club);

                    for ($i = 0; $i < $quantity; $i++) {
                        $clubDetail = new ClbDtl();
                        $clubDetail->OrdId = $order->OrdId;
                        $clubDetail->HndSide =  $club['category']['side'] ?? null;
                        $clubDetail->ClbTypeCd =  $club['category']['category_id'] ?? null;
                        $clubDetail->HeadDsgnId =  $club['category']['model']['model_id'] ?? null;
                        $clubDetail->Qty =  1;
                        $clubDetail->EachAmt = $clubPrice; // Set individual club price
                        $clubDetail->Color = $club['category']['model']['color'] ?? null;
                        $clubDetail->ClbNum = $club['category']['model']['club_numbers'][$i] ?? null;
                        $clubDetail->LieId =  $club['category']['model']['lie_angle_id'] ?? null;
                        $clubDetail->FaceAngleId =  $club['category']['model']['faceangle_id'] ?? null;
                        $clubDetail->LoftId =  $club['category']['model']['loft_id'] ?? null;
                        $clubDetail->HslId =  $club['category']['model']['hossel_id'] ?? null;
                        $clubDetail->ShaftLenIncrement =  $club['category']['shaft_len_increment'] ?? null;
                        $clubDetail->SSTPure =  $club['category']['sst_pure'] ?? null;
                        $clubDetail->ShaftId =  $club['category']['shaft']['name'] ?? null;
                        $clubDetail->ShfTypeCd =  $club['category']['shaft']['material'] ?? null;
                        $clubDetail->ShfFlxCd =  $club['category']['shaft']['flex'] ?? null;
//                        $clubDetail->ShfWgt =  $club['category']['shaft']['weight'] ?? null;
                        $clubDetail->StdShfLen = $club['lengthData']['length'] ?? null;
                        $clubDetail->ShfLenId = $club['lengthData']['range'] ?? null;
                        $clubDetail->GripTypeId =  $club['grip']['type'] ?? null;
                        $clubDetail->TopGripSzId =  $club['grip']['size'] ?? null;
                        $clubDetail->BotGripSzId =  $club['grip']['size'] ?? null;
                        $clubDetail->WrapSide =  $club['grip']['wrap-side'] ?? null;
                        $clubDetail->ExtraWrap =  $club['grip']['extra-wraps'] ?? null;
                        $clubDetail->ActvTypeId = 1;
                        $clubDetail->save();
                    }
                }
            }
            DB::commit();
            Cache::forget('order_form_draft_' . Auth::id());
            return redirect()->back()->with(['title' => 'Done', 'message' => 'Order created successfully','type' => 'success']);
        } catch (\Exception $e){
            DB::rollBack();
            return redirect()->back()->with(['title' => 'Oops', 'message' => 'Something went wrong! ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $customer = Cust::where('CustId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $customer->status = $request->status;
            $customer->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Customer status updated successfully.',
                'status' => $customer->status,
                'resultHtml' => ($customer->status==1)?'<span style="cursor:pointer;" class="success change-customer-status"  customer_id="'.$customer->CustId.'" customer_status="0" >Active</span>':'<span style="cursor:pointer;" class="danger change-customer-status"  customer_id="'.$customer->CustId.'" customer_status="1" >Inactive</span>'
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update fitter status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
//    public function orderSaveDraft(Request $request){
//        $flatFormData = $request->all();
//        $nestedFormData = [];
//        foreach ($flatFormData as $key => $value) {
//            Arr::set($nestedFormData, str_replace(['[', ']'], ['.', ''], $key), $value);
//        }
//        Cache::put('order_form_draft_' . auth()->user()->id, $nestedFormData, now()->addWeek());
//        return response()->json(['message' => 'Draft saved successfully!']);
//    }

    public function orderSaveDraft(Request $request)
    {
        $flatFormData = $request->all();
        $nestedFormData = [];
        foreach ($flatFormData as $key => $value) {
            Arr::set($nestedFormData, str_replace(['[', ']'], ['.', ''], $key), $value);
        }

        $existingCache = Cache::get('order_form_draft_' . auth()->user()->id, []);

        if (isset($nestedFormData['clubs']) && is_array($nestedFormData['clubs'])) {
            $existingClubs = isset($existingCache['clubs']) ? $existingCache['clubs'] : [];

            foreach ($nestedFormData['clubs'] as $newIndex => $newClub) {
                if (isset($existingClubs[$newIndex])) {
                    if ($existingClubs[$newIndex] != $newClub) {
                        $existingClubs[$newIndex] = $newClub;
                    }
                } else {
                    $existingClubs[$newIndex] = $newClub;
                }
            }

            $nestedFormData['clubs'] = $existingClubs;
        }

        Cache::put('order_form_draft_' . auth()->user()->id, $nestedFormData, now()->addWeek());
        return response()->json(['message' => 'Draft saved successfully!']);
    }
}
