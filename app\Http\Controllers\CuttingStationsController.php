<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ClbDtl;
use App\Models\CuttingStation;
use App\Http\Requests\CuttingStationRequest;
use App\Models\Ord;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;


class CuttingStationsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if ($request->ajax()){
            $cuttingStation = Ord::where('production_status', 'Cutting Station')->latest()->get();
            return DataTables::of($cuttingStation)
                ->addIndexColumn()
                ->addColumn('OrdNo', function ($row) {
                    return $row->OrdNo ?? 'N/A';
                })
                ->addColumn('customer', function ($row) {
                    return $row->customer->FirstNm . ' '. $row->customer->LastNm ?? 'N/A';
                })
                ->addColumn('total_qty', fn($row) => optional($row)->clubDetail->count() ?? '0')
                ->addColumn('order_type', fn($row) => optional($row->orderType)->OrdTypeDsc ?? '-')
                ->addColumn('action', function ($row) {
                    $url = url('cuttingstations/' . $row->OrdId);
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website/assets/images/ellipsis.svg') . '">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li>
                                <a class="dropdown-item edit_cutting_button" href="' . $url . '">
                                    View
                                </a>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['OrdNo','customer','total_qty','order_type','action'])
                ->make(true);
        }
        return view('cuttingstations.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create(Request $request)
    {
        $club = ClbDtl::with(['model', 'category', 'shaft', 'lie', 'faceAngle', 'shaftWeight', 'shaftFlex', 'shaftMaterial', 'shaftLength'])->find($request->clubId);
        if (!$club) {
            return response()->json(['error' => 'Club not found'], 404);
        }
        $html = view('cuttingstations.create', compact('club'))->render();
        $title = $club->model->HeadDsgnCd .' - '. $club->model->HeadDsgnDsc;
        if ($club->cuttingStation != null){
            $shaftWeight = $club->cuttingStation->shaft_weight;
            $shaftFrequency = $club->cuttingStation->shaft_frequency;
        }else{
            $shaftWeight = null;
            $shaftFrequency = null;
        }
        return response()->json(['html' => $html,'title' => $title,'shaftWeight' => $shaftWeight,'shaftFrequency' => $shaftFrequency]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Check if this is a bulk submission (array format) or individual submission
        if ($request->has('shaft_weight') && is_array($request->input('shaft_weight'))) {
            // Bulk submission from table
            $this->handleBulkSubmission($request);
        } else {
            // Individual submission from modal
            $this->handleIndividualSubmission($request);
        }

        return redirect()->back()->with(['title'=>'Done','message'=>'Update successfully','type'=>'success']);
    }

    /**
     * Handle individual club submission from modal
     *
     * @param Request $request
     * @return void
     */
    private function handleIndividualSubmission(Request $request)
    {
        $request->validate([
            'order_id' => 'required',
            'club_id' => 'required',
            'staff_id' => 'required',
            'shaft_weight' => 'required|numeric|min:0',
            'shaft_frequency' => 'required|numeric|min:0',
        ]);

        CuttingStation::updateOrCreate(
            [
                'order_id' => $request->input('order_id'),
                'club_id' => $request->input('club_id'),
            ],
            [
                'staff_id' => $request->input('staff_id'),
                'shaft_weight' => $request->input('shaft_weight'),
                'shaft_frequency' => $request->input('shaft_frequency'),
            ]
        );
    }

    /**
     * Handle bulk club submission from table
     *
     * @param Request $request
     * @return void
     */
    private function handleBulkSubmission(Request $request)
    {
        $request->validate([
            'order_id' => 'required',
            'staff_id' => 'required',
            'shaft_weight' => 'required|array',
            'shaft_frequency' => 'required|array',
            'shaft_weight.*' => 'nullable|numeric|min:0',
            'shaft_frequency.*' => 'nullable|numeric|min:0',
        ]);

        $shaftWeights = $request->input('shaft_weight');
        $shaftFrequencies = $request->input('shaft_frequency');
        $orderId = $request->input('order_id');
        $staffId = $request->input('staff_id');

        foreach ($shaftWeights as $clubId => $shaftWeight) {
            // Only process if both shaft weight and frequency are provided
            if (!empty($shaftWeight) && !empty($shaftFrequencies[$clubId])) {
                CuttingStation::updateOrCreate(
                    [
                        'order_id' => $orderId,
                        'club_id' => $clubId,
                    ],
                    [
                        'staff_id' => $staffId,
                        'shaft_weight' => $shaftWeight,
                        'shaft_frequency' => $shaftFrequencies[$clubId],
                    ]
                );
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $order = Ord::findOrFail($id);
        return view('cuttingstations.show',['order'=>$order]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $cuttingstation = CuttingStation::findOrFail($id);
        return view('cuttingstations.edit',['cuttingstation'=>$cuttingstation]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  CuttingStationRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(CuttingStationRequest $request, $id)
    {
        $cuttingstation = CuttingStation::findOrFail($id);
		$cuttingstation->order_id = $request->input('order_id');
		$cuttingstation->club_id = $request->input('club_id');
		$cuttingstation->staff_id = $request->input('staff_id');
		$cuttingstation->shaft_weight = $request->input('shaft_weight');
		$cuttingstation->shaft_frequency = $request->input('shaft_frequency');
        $cuttingstation->save();

        return to_route('cuttingstations.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $cuttingstation = CuttingStation::findOrFail($id);
        $cuttingstation->delete();

        return to_route('cuttingstations.index');
    }
}
