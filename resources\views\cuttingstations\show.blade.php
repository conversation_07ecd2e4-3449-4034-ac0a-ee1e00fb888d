@extends('theme.layout.master')
@push('css')
    <style>
        .club-detail-section {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .club-model-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2d8b57;
            margin-top: 10px;
            margin-bottom: 8px;
            text-decoration: underline;
        }

        .spec-detail p {
            font-size: 1rem;
            margin: 5px 0;
            color: #555;
        }

        .spec-detail strong {
            color: #333;
        }

        .spec-detail p:last-child {
            margin-bottom: 0;
        }

    </style>
    <style>
        /* Base Styles */
        .submitted_survey_sec {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /*.pipeline_stages_scroll {*/
        /*    overflow-x: scroll;*/
        /*    padding-bottom: 15px;*/
        /*}*/

        .pipeline_stages_scroll::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,.1);
            border-radius: 8px;
            background-color: #F5F5F5;
        }

        .pipeline_stages_scroll::-webkit-scrollbar {
            width: 10px;
            height: 8px;
        }

        .pipeline_stages_scroll::-webkit-scrollbar-thumb {
            border-radius: 8px;
            -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.1);
            background-color: #e0e0e0;
        }

        .pipeline_stages_div, .dragable_row_main_ {
            display: flex;
            overflow-x: auto;
            width: fit-content;
            flex-direction: row;
            flex-wrap: nowrap;
        }

        /* Stage Columns */
        .submitted_survey_sec .pipeline_stages__box {
            width: 100%;
            padding: 0 8px;
        }

        .submitted_survey_sec .inner_section_submitted_survey {
            padding: 5px 5px 10px 5px;
            border-radius: 8px;
            background: #3a994a45;
            position: relative;
        }
        .submitted_survey_sec .inner_section_submitted_survey:before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(92deg, #48B64C -14.76%, #205021 125.87%);
            border-radius: 0px 0px 8px 8px;
        }
        .submitted_survey_sec .survey_title h4 {
            font-size: 13px;
            margin: 0;
            font-family: 'Poppins-Regular';
            color: var(--black);
            font-weight: 600;
            text-align: center;
        }

        /* Opportunity Cards */
        .submitted_survey_sec .dragable_row_main_ .column {
            width: 200px;
            min-width: 200px;
            padding: 15px 0 0 0;
        }

        .submitted_survey_sec .dragable_row_main_ .column > .portlet {
            padding: 0 10px;
            width: 100%;
            border: 0;
        }

        .portlet {
            margin: 15px 0 15px 0;
        }

        .inner_section_dragable {
            background: #fff;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .inner_section_dragable:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .card_header {
            margin-bottom: 4px;
        }

        .header_main_wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .userImg_name_wrapper {
            display: flex;
            align-items: center;
        }

        .user_image {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .user_name_date .user_name h4 a {
            color: var(--black);
            text-decoration: none;
            font-family: 'Poppins-SemiBold';
            padding: 0px 0 0 8px;
            font-size: 12px;
        }

        .action_btn button {
            background: transparent;
            border: none;
            color: #6c757d;
            font-size: 16px;
            padding: 0 4px;
        }

        .card_footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 4px;
            border-top: 1px solid #f0f0f0;
        }

        .card_footer .price label,
        .card_footer .quantity label {
            color: var(--black);
            font-weight: 600;
            font-size: 12px;
            font-family: 'Poppins-Medium';
        }

        .card_footer .price label span,
        .card_footer .quantity label span {
            color: var(--black);
            font-weight: 600;
            font-size: 12px;
            font-family: 'Poppins-Medium';
        }

        /* Dropdown Menu */
        .dropdown-menu {
            padding: 8px 0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }

        .dropdown-item {
            padding: 6px 16px;
            font-size: 14px;
            color: #333;
        }
        /* Drag and Drop Placeholder */
        .portlet-placeholder {
            border: 2px dashed #dee2e6;
            background: #f8f9fa;
            margin: 0 0 15px 0;
            height: 100px;
            border-radius: 8px;
        }
        .submitted_survey_sec .dragable_row_main_ .column .header_main_wrapper .action_btn ul.dropdown-menu.show a {
            padding: 0;
        }
        #orderDetailsContent {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        #orderDetailsContent .club-detail-section {
            width: 48%;
        }
    </style>
@endpush
@section('content')
    <section class="view-dashboard view_profile_management">
        <div class="container-fluid">
            <div class="row custom_row_gap">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row customize_row">
                                    <div class="col-md-6">
                                        <div class="user_info">
                                            <div class="custom_justify">
                                                <h1><strong>ORDER NO :</strong> {{ $order->OrdNo ?? '' }}</h1>
                                            </div>
                                            <ul class="list-unstyled">
                                                <li>
                                                    <i class="fa-solid fa-user"></i>{{ $order->customer->FirstNm ?? '' }} {{ $order->customer->LastNm ?? '' }}
                                                </li>
                                                @if(isset($order->customer->PhnNum))
                                                    <li><i class="fa-solid fa-phone"></i>{{  $order->customer->PhnNum ?? ''  }}
                                                    </li>
                                                @endif
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="custom_card_management">
                                            <img src="{{ asset('website') }}/assets/images/card-bar.svg">
                                            <h2>{{ $order->clubDetail->sum('Qty') ?? 1 }}</h2>
                                            <h5>Total Products</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="fitter_view_tabs">
                                    <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-general-tab" data-bs-toggle="pill" data-bs-target="#pills-general" type="button" role="tab" aria-controls="pills-general" aria-selected="true">
                                                <img class="img-fluid" src="{{ asset('website/assets/images/information.svg') }}"/>
                                                Customer Info
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="pills-status-tab" data-bs-toggle="pill" data-bs-target="#pills-status" type="button" role="tab" aria-controls="pills-status" aria-selected="false">
                                                <img class="img-fluid" src="{{ asset('website/assets/images/status-up.png') }}"/>
                                                Production Management
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <!-- Tabs Content -->
                        <div class="tab-content" id="pills-tabContent">
                            <!-- Customer Info Tab -->
                            <div class="tab-pane fade" id="pills-general" role="tabpanel" aria-labelledby="pills-general-tab" tabindex="0">
                                <div class="user_general_info">
                                    <h3>Customer Info</h3>
                                    <div class="general_detail">
                                        <div class="txt_field"><label>Name</label>
                                            <span>{{ $order->customer->FirstNm ?? '' }} {{ $order->customer->LastNm ?? '' }}</span>
                                        </div>
                                        <div class="txt_field"><label>E-Mail</label>
                                            <span>{{ $order->customer->EMailAddr ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Phone Number</label>
                                            <span>{{ $order->customer->PhnNum ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Street</label>
                                            <span>{{ $order->customer->Addr ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>City/Town</label>
                                            <span>{{ $order->customer->CityNm ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>State/Province/Region</label>
                                            <span>{{ $order->customer->StateCd ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Zip/Postal Code</label>
                                            <span>{{ $order->customer->PostalCd ?? 'N/A' }}</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- Pricing Tab -->
                            <div class="tab-pane fade show active" id="pills-status" role="tabpanel" aria-labelledby="pills-status-tab">
                                <form action="{{route('cuttingstations.store')}}" method="post">
                                    @csrf
                                    <input type="hidden" name="order_id" value="{{$order->OrdId??''}}">
                                    <input type="hidden" name="staff_id" value="{{Auth::id()??''}}">
                                    <div class="table_header in_line">
                                        <div class="user_engagement">
                                            <h3>Cutting Station</h3>
                                        </div>
                                        <div class="side_fields">
                                            <div class="custom_search_box">
                                                <div class="txt_field">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                    <input type="search" placeholder="Search" class="form-control searchinput">
                                                </div>
                                            </div>
                                            <div class="submit_station_btn">
                                                <button type="submit" class="btn light_green_btn">Submit</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="datatable table">
                                            <thead>
                                            <tr>
                                                <th>SR#</th>
                                                <th>Order #</th>
                                                <th>Model Name</th>
                                                <th>Customer Name</th>
                                                <th>Shaft Weight (grams)</th>
                                                <th>Shaft Frequency (CPM)</th>
                                                <th>Action</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @foreach($order->clubDetail as $key => $club)
                                                <tr>
                                                    <td>{{ $loop->iteration??$club->id }}</td>
                                                    <td>{{$order->OrdNo??''}}</td>
                                                    <td>{{ $club->model->HeadDsgnCd ?? '' }} - {{ $club->model->HeadDsgnDsc ?? '' }}</td>
                                                    <td class="rounded-start">
                                                        <div class="">{{$order->customer->FirstNm??''}} {{$order->customer->LastNm??''}}</div>
                                                    </td>
                                                    <td>
                                                        <input type="text" name="shaft_weight[{{$club->ClbDtlId}}]" class="form-control" value="{{$club->cuttingStation->shaft_weight ?? ''}}" oninput="formatNumericInput(this, 2000)">
                                                    </td>
                                                    <td>
                                                        <input type="text" name="shaft_frequency[{{$club->ClbDtlId}}]" class="form-control" value="{{$club->cuttingStation->shaft_frequency ?? ''}}" oninput="formatNumericInput(this, 500)">
                                                    </td>
                                                    <td>
                                                        <div class="dropdown">
                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <img class="ellipsis_img" src="{{ asset('website') }}/assets/images/ellipsis.svg">
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                                                                <li>
                                                                    <a class="dropdown-item view_detail" data-bs-toggle="modal" data-bs-target="#orderDetailModal" onclick="showOrderDetail({{$club->ClbDtlId??''}})">View</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Order Detail Modal -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1" aria-labelledby="orderDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderDetailModalLabel">Order Detail</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-xmark"></i></button>

                </div>
                <div class="modal-body">
                    <form class="cutting_station_club" action="{{route('cuttingstations.store')}}" method="post">
                        @csrf
                        <h5 class="club-model-title"></h5>
                        <div id="orderDetailsContent">

                        </div>
                        <div class="row">
                            <input type="hidden" name="order_id" value="{{$order->OrdId??''}}">
                            <input type="hidden" name="staff_id" value="{{Auth::id()??''}}">
                            <input type="hidden" name="club_id" id="modal_club_id" value="">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Shaft Weight (grams)</label>
                                    <input type="text" class="form-control shaft_weight" name="shaft_weight" oninput="formatNumericInput(this, 2000)" id="shaft_weight" step="0.1">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Shaft Frequency (CPM)</label>
                                    <input type="text" class="form-control shaft_frequency" name="shaft_frequency" oninput="formatNumericInput(this, 500)"  id="shaft_frequency">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn cutting_station_club">Update</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <script>
        function showOrderDetail(clubId) {
            $('#orderDetailsContent').html('<p>Loading...</p>');
            // Set the club_id in the hidden input
            $('#modal_club_id').val(clubId);

            $.ajax({
                url: "{{ route('cuttingstations.create', ['clubId' => '__clubId__']) }}".replace('__clubId__', clubId),
                method: 'GET',
                success: function (data) {
                    if (data.html) {
                        $('.club-model-title').html(data.title);
                        $('#orderDetailsContent').html(data.html);
                        $('.shaft_weight').val(data.shaftWeight);
                        $('.shaft_frequency').val(data.shaftFrequency);
                    } else {
                        alert('No details available for this club.');
                    }
                },
                error: function (xhr, status, error) {
                    alert('Error loading order details.');
                }
            });
        }
        function formatNumericInput(input, maxValue = 100000) {
            let rawValue = input.value.replace(/\D/g, '');
            let numericValue = parseInt(rawValue || '0', 10);
            if (numericValue > maxValue) {
                numericValue = maxValue;
            }
            // input.value = numericValue.toLocaleString('en-US');
            input.value = numericValue.toString();
        }
        $(document).ready(function () {
            $('.cutting_station_club').on('submit', function () {
                let submitBtn =  $(this).find('button[type="submit"]');
                submitBtn.prop('disabled', true).text('Updating...');
            });
        });
    </script>
@endpush

