@extends('theme.layout.master')

@push('css')
@endpush

@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Pending/Quotation</h5>
                            </div>
                            <div class="side_fields">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                                <a href="{{url('Quotation/create')}}" class="btn dark_green_btn"><i class="fa-solid fa-square-plus"></i> Create </a>
                                <!-- Filter Dropdown -->
{{--                                <div class="dropdown-btn">--}}
{{--                                    <button type="button" class="btn dropdown-toggle light_green_btn" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">--}}
{{--                                        <i class="fas fa-filter"></i> Filter--}}
{{--                                    </button>--}}
{{--                                    <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">--}}
{{--                                        <div class="dropdown_top">--}}
{{--                                            <h6 class="">Filter</h6>--}}
{{--                                            <button type="button" class="btn_close" data-bs-dismiss="dropdown" aria-label="Close">--}}
{{--                                                <i class="fas fa-times"></i>--}}
{{--                                            </button>--}}
{{--                                        </div>--}}
{{--                                        <form>--}}
{{--                                            <div class="form-group">--}}
{{--                                                <label for="role">Role</label>--}}
{{--                                                <select id="role" class="form-control" name="role">--}}
{{--                                                    <option value="">Select Role</option>--}}
{{--                                                    <option value="admin">Admin</option>--}}
{{--                                                    <option value="user">User</option>--}}
{{--                                                    <option value="manager">Manager</option>--}}
{{--                                                </select>--}}
{{--                                            </div>--}}
{{--                                            <div class="form-group">--}}
{{--                                                <label for="status">Status</label>--}}
{{--                                                <select id="status" class="form-control" name="status">--}}
{{--                                                    <option value="">Select Status</option>--}}
{{--                                                    <option value="active">Active</option>--}}
{{--                                                    <option value="inactive">Inactive</option>--}}
{{--                                                    <option value="pending">Pending</option>--}}
{{--                                                </select>--}}
{{--                                            </div>--}}
{{--                                            <div class="dropdown_bottom">--}}
{{--                                                <button type="submit" class="btn light_green_btn">Apply Filter</button>--}}
{{--                                                <button type="button" class="btn cancel_btn" data-bs-dismiss="dropdown">Cancel</button>--}}
{{--                                            </div>--}}
{{--                                        </form>--}}

{{--                                    </div>--}}
{{--                                </div>--}}
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="pending-orders-datatable table table-row-bordered gy-5">
                                <thead>
                                <tr>
                                    <th>SR#</th>
                                    <th>Order No</th>
                                    <th>Customer</th>
                                    <th>Fitter</th>
                                    <th>Total Qty</th>
                                    <th>Total Price</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('js')
    <script>
        $(document).ready(function () {
            var status = "{{ $status }}";

            var table = $('.pending-orders-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('orders.index', ['status' => '']) }}' + '/' + status, // Append the status in the URL
                    type: 'GET',
                    dataSrc: 'data'
                },
                columns: [
                    {data: null, name: null, defaultContent: ''},
                    {data: 'OrdNo', name: 'OrdNo'},
                    {data: 'customer', name: 'customer'},
                    {data: 'fitter', name: 'fitter'},
                    {data: 'total_qty', name: 'total_qty'},
                    {data: 'total_price', name: 'total_price'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(0)').html(dataIndex + 1);
                },
                initComplete: function () {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

            $(".filter-form").submit(function (e) {
                e.preventDefault();
                table.draw();
            });
        });

    </script>
@endpush
